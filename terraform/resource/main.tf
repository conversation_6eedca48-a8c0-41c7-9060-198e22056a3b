terraform {
  backend "pg" {
    conn_str             = ""
    schema_name          = ""
    skip_schema_creation = false
  }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "3.4.3"
    }
    local = {
      source  = "hashicorp/local"
      version = "2.5.1"
    }
    kubectl = {
      source  = "gavi<PERSON><PERSON><PERSON>/kubectl"
      version = ">= 1.7.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

data "digitalocean_kubernetes_cluster" "cluster" {
  name = jsondecode(data.http.deployments.body)["data"]["clusters"]["name"]
}


locals {
  # Create array with format "{namespace.name}-{namespace.id}-{service.name}"
  namespace_service_array = flatten([
    for namespace in jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"] : [
      for service in namespace.services : "${namespace.name}-${namespace.id}-${service.name}"
    ]
  ])

  namespace_active_array = flatten([
    for namespace in jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"] : [
      namespace.is_active
    ]
  ])

  # Flatten deployment list for external secrets
  deployment_flat_list = flatten([
    for namespace in jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"] : [
      for deployment in namespace.deployments : {
        project = namespace.name
        deployment_name = deployment.name
        namespace = namespace.namespace
      }
    ]
  ])
}

provider "kubernetes" {
  host    = data.digitalocean_kubernetes_cluster.cluster.endpoint
  token   = data.digitalocean_kubernetes_cluster.cluster.kube_config[0].token
  cluster_ca_certificate = base64decode(
    data.digitalocean_kubernetes_cluster.cluster.kube_config[0].cluster_ca_certificate
  )
}

# provider "kubernetes" {
#   config_path = "../cluster/kubeconfig/${jsondecode(data.http.deployments.body)["data"]["clusters"]["name"]}-kubeconfig.yaml"
# }

resource "kubernetes_namespace" "namespace" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])
  metadata {
    name = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
  }
}


# DockerHub registry secret for private image pulling
resource "kubernetes_secret" "dockerhub_registry" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])

  metadata {
    name      = "dockerhub-registrykey"
    namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
  }

  type = "kubernetes.io/dockerconfigjson"

  data = {
    ".dockerconfigjson" = jsonencode({
      auths = {
        "https://index.docker.io/v1/" = {
          "username" = var.dockerhub_username
          "password" = var.dockerhub_password
          "email"    = var.dockerhub_email
          "auth"     = base64encode("${var.dockerhub_username}:${var.dockerhub_password}")
        }
      }
    })
  }

  depends_on = [kubernetes_namespace.namespace]
}

# AWS credentials secret for AWS services
resource "kubernetes_secret" "aws_credentials" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])

  metadata {
    name      = "aws-credentials"
    namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
  }

  type = "Opaque"

  data = {
    "AWS_ACCESS_KEY_ID"     = var.aws_access_key_id
    "AWS_SECRET_ACCESS_KEY" = var.aws_secret_access_key
  }

  depends_on = [kubernetes_namespace.namespace]
}


# AWS SecretStore for external-secrets operator
resource "kubernetes_manifest" "aws_secretstore" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])

  manifest = {
    apiVersion = "external-secrets.io/v1"
    kind       = "SecretStore"
    metadata = {
      name      = "aws-secretstore-${jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace}"
      namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
    }
    spec = {
      provider = {
        aws = {
          service = "SecretsManager"
          region  = "ap-southeast-1"
          auth = {
            secretRef = {
              accessKeyIDSecretRef = {
                name = "aws-credentials"
                key  = "AWS_ACCESS_KEY_ID"
              }
              secretAccessKeySecretRef = {
                name = "aws-credentials"
                key  = "AWS_SECRET_ACCESS_KEY"
              }
            }
          }
        }
      }
    }
  }

  depends_on = [kubernetes_secret.aws_credentials]
}

# External Secrets for each deployment to sync environment variables from AWS Secrets Manager
resource "kubernetes_manifest" "external_secrets" {
  count = sum([
    for namespace in jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"] :
    length(namespace.deployments)
  ])

  manifest = {
    apiVersion = "external-secrets.io/v1"
    kind       = "ExternalSecret"
    metadata = {
      name      = "${local.deployment_flat_list[count.index].project}-${local.deployment_flat_list[count.index].deployment_name}-env-external"
      namespace = local.deployment_flat_list[count.index].namespace
    }
    spec = {
      refreshInterval = "1m"
      secretStoreRef = {
        name = "aws-secretstore-${local.deployment_flat_list[count.index].namespace}"
        kind = "SecretStore"
      }
      target = {
        name = "${local.deployment_flat_list[count.index].project}-${local.deployment_flat_list[count.index].deployment_name}-env-sync"
        creationPolicy = "Owner"
      }
      dataFrom = [{
        extract = {
          key = var.env == "production" ? "blacking-${local.deployment_flat_list[count.index].deployment_name}-env-master" : "blacking-${local.deployment_flat_list[count.index].deployment_name}-env-master-dev"
        }
      }]
    }
  }

  depends_on = [kubernetes_manifest.aws_secretstore]
}


module "deployment_resource" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])
  source = "../modules/kubernetes/modules/deployment"
  project = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].name
  namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
  deployments = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].deployments

  depends_on = [
    kubernetes_namespace.namespace ,
    kubernetes_secret.dockerhub_registry,
    kubernetes_secret.aws_credentials,
    kubernetes_manifest.aws_secretstore
  ]
}

module "service_resource" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])
  source = "../modules/kubernetes/modules/service"
  project = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].name
  namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
  services = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].services

  depends_on = [
    kubernetes_namespace.namespace ,
    kubernetes_secret.dockerhub_registry,
    kubernetes_secret.aws_credentials,
    kubernetes_manifest.aws_secretstore
  ]
}

module "ingress_resource" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])
  source = "../modules/kubernetes/modules/ingress"
  project = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].name
  namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].namespace
  ingress = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].ingress

  depends_on = [
    kubernetes_namespace.namespace ,
    kubernetes_secret.dockerhub_registry,
    kubernetes_secret.aws_credentials,
    kubernetes_manifest.aws_secretstore
  ]
}

# data "kubernetes_service" "nginx-ip-address" {
#   metadata {
#     name = "${jsondecode(data.http.deployments.body)["data"]["clusters"]["name"]}-ingress-nginx-controller"
#     namespace = "ingress-nginx"
#   }
# }


# Daily Commission CronJob - Static configuration for blacking-core namespace
resource "kubernetes_manifest" "daily_commission_cronjob" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])
  manifest = {
    apiVersion = "batch/v1"
    kind       = "CronJob"
    metadata = {
      name      = "daily-commission-cronjob"
      namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].name
    }
    spec = {
      schedule  = "30 11 * * *"  # Daily at 11:30 AM
      timeZone  = "Asia/Bangkok"
      jobTemplate = {
        spec = {
          ttlSecondsAfterFinished = 5    # Delete job 5 seconds after completion
          activeDeadlineSeconds   = 300  # Terminate job if it runs longer than 5 minutes
          backoffLimit           = 2     # Retry failed jobs up to 2 times
          template = {
            spec = {
              containers = [
                {
                  name            = "daily-commission-curl"
                  image           = "curlimages/curl:latest"
                  imagePullPolicy = "IfNotPresent"
                  command = [
                    "/bin/sh",
                    "-c",
                    <<-EOT
                      echo "Starting daily commission API call at $(date)"

                      # Calculate yesterday's date in YYYY-MM-DD format
                      yesterday=$(date -d "yesterday" +%Y-%m-%d)
                      echo "Processing data for date: $yesterday"

                      # Prepare JSON payload
                      json_payload="{\"date\": \"$yesterday\"}"
                      echo "JSON payload: $json_payload"

                      # Make POST request with JSON body
                      response=$(curl -s -w "HTTPSTATUS:%%{http_code}" \
                        --location 'https://${jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index]["ingress"][0]["api_host"]}/api/v1/game-transactions/daily-commission-process' \
                        --header 'Content-Type: application/json' \
                        --data "$json_payload")

                      http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
                      body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

                      echo "HTTP Status Code: $http_code"
                      echo "Response Body: $body"

                      if [ $http_code -eq 200 ]; then
                        echo "Daily commission API call successful"
                        exit 0
                      else
                        echo "Daily commission API call failed with status code: $http_code"
                        exit 1
                      fi
                    EOT
                  ]
                }
              ]
              restartPolicy = "OnFailure"
            }
          }
        }
      }
      successfulJobsHistoryLimit = 3
      failedJobsHistoryLimit     = 1
    }
  }

  depends_on = [
    kubernetes_namespace.namespace ,
    kubernetes_secret.dockerhub_registry,
    kubernetes_secret.aws_credentials,
    kubernetes_manifest.aws_secretstore,
    module.ingress_resource
  ]
}

# Daily Commission CronJob - Static configuration for blacking-core namespace
resource "kubernetes_manifest" "daily-sync-cronjob" {
  count = length(jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"])
  manifest = {
    apiVersion = "batch/v1"
    kind       = "CronJob"
    metadata = {
      name      = "daily-sync-cronjob"
      namespace = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index].name
    }
    spec = {
      schedule  = "30 11 * * *"  # Daily at 11:30 AM
      timeZone  = "Asia/Bangkok"
      jobTemplate = {
        spec = {
          ttlSecondsAfterFinished = 5    # Delete job 5 seconds after completion
          activeDeadlineSeconds   = 300  # Terminate job if it runs longer than 5 minutes
          backoffLimit           = 2     # Retry failed jobs up to 2 times
          template = {
            spec = {
              containers = [
                {
                  name            = "daily-sync-curl"
                  image           = "curlimages/curl:latest"
                  imagePullPolicy = "IfNotPresent"
                  command = [
                    "/bin/sh",
                    "-c",
                    <<-EOT
                      echo "Starting daily commission API call at $(date)"

                      # Calculate yesterday's date in YYYY-MM-DD format
                      yesterday=$(date -d "yesterday" +%Y-%m-%d)
                      echo "Processing data for date: $yesterday"

                      # Prepare JSON payload
                      json_payload="{\"date\": \"$yesterday\"}"
                      echo "JSON payload: $json_payload"

                      # Make POST request with JSON body
                      response=$(curl -s -w "HTTPSTATUS:%%{http_code}" \
                        --location 'https://${jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index]["ingress"][0]["api_host"]}/api/v1/game-transactions/daily-sync' \
                        --header 'Content-Type: application/json' \
                        --data "$json_payload")

                      http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
                      body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

                      echo "HTTP Status Code: $http_code"
                      echo "Response Body: $body"

                      if [ $http_code -eq 200 ]; then
                        echo "Daily commission API call successful"
                        exit 0
                      else
                        echo "Daily commission API call failed with status code: $http_code"
                        exit 1
                      fi
                    EOT
                  ]
                }
              ]
              restartPolicy = "OnFailure"
            }
          }
        }
      }
      successfulJobsHistoryLimit = 3
      failedJobsHistoryLimit     = 1
    }
  }

  depends_on = [
    kubernetes_namespace.namespace ,
    kubernetes_secret.dockerhub_registry,
    kubernetes_secret.aws_credentials,
    kubernetes_manifest.aws_secretstore,
    module.ingress_resource
  ]
}

module "cloudflare" {
  count = jsondecode(data.http.deployments.body)["data"]["clusters"]["namespaces"][count.index]["ingress"][0]["is_master"] ? 1 : 0
  source = "../modules/cloudflare"
  ip_address = local.namespace_active_array[0] == true ? jsondecode(data.http.deployments.body)["data"]["clusters"]["load_balance_ip"] : "google.com"
  type = local.namespace_active_array[0] == true ? "A" : "CNAME"
  cloudflare_api_token = var.cloudflare_api_token
  cloudflare_zone_id = var.cloudflare_zone_id
  sub_domain_list = local.namespace_service_array
}

